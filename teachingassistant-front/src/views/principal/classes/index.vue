<template>
  <div class="classes-container">
    <div class="header">
      <h2>班级管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加班级
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="班级名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入班级名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="班主任">
          <el-select
            v-model="searchForm.teacherId"
            placeholder="请选择班主任"
            clearable
          >
            <el-option
              v-for="teacher in teachers"
              :key="teacher.userId"
              :label="teacher.realName"
              :value="teacher.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="classId" label="ID" width="80" />
        <el-table-column prop="name" label="班级名称" />
        <el-table-column label="班主任" width="120">
          <template #default="scope">
            {{ scope.row.mainTeacherInfo?.realName || '未分配' }}
          </template>
        </el-table-column>
        <el-table-column prop="studentCount" label="学生人数" width="100">
          <template #default="scope">
            {{ scope.row.studentCount || 0 }}人
          </template>
        </el-table-column>
        <el-table-column prop="teacherCount" label="任课老师" width="100">
          <template #default="scope">
            {{ scope.row.teacherCount || 0 }}人
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ new Date(scope.row.createdAt).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleViewStudents(scope.row)"
            >
              学生列表
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleViewSchedule(scope.row)"
            >
              课表
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="班级名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入班级名称" />
        </el-form-item>
        <el-form-item label="班主任">
          <el-select v-model="form.mainTeacher" placeholder="请选择班主任" style="width: 100%" clearable>
            <el-option
              v-for="teacher in teachers"
              :key="teacher.userId"
              :label="teacher.realName"
              :value="teacher.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { principalSchoolClassApi } from '@/api/schoolClass'
import { principalTeacherApi } from '@/api/teacher'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const teachers = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  teacherId: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  classId: null,
  name: '',
  mainTeacher: null
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入班级名称', trigger: 'blur' }
  ]
}

// 方法
const fetchTeachers = async () => {
  try {
    const response = await principalTeacherApi.getAll()
    teachers.value = response.data.map(teacher => ({
      userId: teacher.userId,
      realName: teacher.realName
    }))
  } catch (error) {
    console.error('获取老师列表失败:', error)
    ElMessage.error('获取老师列表失败')
  }
}

const fetchData = async () => {
  loading.value = true
  try {
    const response = await principalSchoolClassApi.getList({
      page: pagination.page,
      size: pagination.size,
      name: searchForm.name
    })

    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.teacherId = ''
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  dialogTitle.value = '添加班级'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑班级'
  form.classId = row.classId
  form.name = row.name
  form.mainTeacher = row.mainTeacher
  dialogVisible.value = true
}

const handleViewStudents = (row) => {
  ElMessage.info(`查看${row.name}的学生列表功能待开发`)
}

const handleViewSchedule = (row) => {
  ElMessage.info(`查看${row.name}的课表功能待开发`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除班级"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await principalSchoolClassApi.delete(row.classId)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除班级失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    const data = {
      name: form.name,
      mainTeacher: form.mainTeacher
    }

    if (form.classId) {
      // 编辑
      await principalSchoolClassApi.update(form.classId, data)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await principalSchoolClassApi.create(data)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('保存班级失败:', error)
    ElMessage.error('保存失败')
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

const resetForm = () => {
  form.classId = null
  form.name = ''
  form.mainTeacher = null
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchTeachers()
  fetchData()
})
</script>

<style scoped>
.classes-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>

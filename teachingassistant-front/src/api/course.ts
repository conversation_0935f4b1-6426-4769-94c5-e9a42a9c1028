import { request } from '@/utils/request'
import type { ApiResponse, PageResult } from '@/types'

// 课程管理相关类型定义
export interface CourseListParams {
  page?: number
  size?: number
  schoolId?: number
  teacherId?: number
  classroomId?: number
  status?: 'scheduled' | 'completed' | 'cancelled'
}

export interface CourseByDateRangeParams {
  startDate: string
  endDate: string
  teacherId?: number
  classroomId?: number
}

export interface AvailableSlotsParams {
  teacherId: number
  classroomId: number
  startDate: string
  endDate: string
}

export interface CreateCourseData {
  teacherId: number
  studentId: number
  classroomId: number
  courseDate: string
  startTime: string
  endTime: string
  price: number
  gradeLevel?: string
  status?: 'scheduled' | 'completed' | 'cancelled'
}

export interface UpdateCourseData {
  teacherId?: number
  studentId?: number
  classroomId?: number
  courseDate?: string
  startTime?: string
  endTime?: string
  price?: number
  gradeLevel?: string
  status?: 'scheduled' | 'completed' | 'cancelled'
}

export interface CourseInfo {
  courseId: number
  schoolId: number
  teacherId: number
  studentId: number
  classroomId: number
  courseDate: string
  startTime: string
  endTime: string
  price: number
  gradeLevel: string
  status: 'scheduled' | 'completed' | 'cancelled'
  createdAt: string
  updatedAt: string
  teacher?: {
    userId: number
    realName: string
    phone: string
    email: string
  }
  student?: {
    studentId: number
    name: string
    contactPhone: string
  }
  classroom?: {
    classroomId: number
    name: string
    floor: number
    type: string
  }
}

export interface TimeSlot {
  startTime: string
  endTime: string
  available: boolean
}

export interface CourseStatistics {
  totalCourses: number
  scheduledCourses: number
  completedCourses: number
  cancelledCourses: number
}

/**
 * 校长端课程管理API
 */
export const principalCourseApi = {
  /**
   * 获取课程列表（分页）
   */
  getList(params: CourseListParams): Promise<ApiResponse<PageResult<CourseInfo>>> {
    return request.get('/principal/courses', params)
  },

  /**
   * 获取指定日期范围内的课程
   */
  getByDateRange(params: CourseByDateRangeParams): Promise<ApiResponse<CourseInfo[]>> {
    return request.get('/principal/courses/date-range', params)
  },

  /**
   * 获取空闲时段
   */
  getAvailableSlots(params: AvailableSlotsParams): Promise<ApiResponse<Record<string, TimeSlot[]>>> {
    return request.get('/principal/courses/available-slots', params)
  },

  /**
   * 根据ID获取课程详情
   */
  getById(courseId: number): Promise<ApiResponse<CourseInfo>> {
    return request.get(`/principal/courses/${courseId}`)
  },

  /**
   * 创建课程
   */
  create(data: CreateCourseData): Promise<ApiResponse<CourseInfo>> {
    return request.post('/principal/courses', data)
  },

  /**
   * 更新课程信息
   */
  update(courseId: number, data: UpdateCourseData): Promise<ApiResponse<CourseInfo>> {
    return request.put(`/principal/courses/${courseId}`, data)
  },

  /**
   * 删除课程
   */
  delete(courseId: number): Promise<ApiResponse<void>> {
    return request.delete(`/principal/courses/${courseId}`)
  },

  /**
   * 批量删除课程
   */
  batchDelete(courseIds: number[]): Promise<ApiResponse<void>> {
    return request.delete('/principal/courses', { data: courseIds })
  },

  /**
   * 更新课程状态
   */
  updateStatus(courseId: number, status: string): Promise<ApiResponse<void>> {
    return request.patch(`/principal/courses/${courseId}/status`, null, { params: { status } })
  },

  /**
   * 获取课程统计信息
   */
  getStatistics(startDate: string, endDate: string): Promise<ApiResponse<CourseStatistics>> {
    return request.get('/principal/courses/statistics', { startDate, endDate })
  }
}

/**
 * 管理员端课程管理API
 */
export const adminCourseApi = {
  /**
   * 获取课程列表（分页）
   */
  getList(params: CourseListParams): Promise<ApiResponse<PageResult<CourseInfo>>> {
    return request.get('/admin/courses', params)
  },

  /**
   * 根据ID获取课程详情
   */
  getById(courseId: number): Promise<ApiResponse<CourseInfo>> {
    return request.get(`/admin/courses/${courseId}`)
  }
}

import { request } from '@/utils/request'
import type { ApiResponse, PageResult } from '@/types'

// 学生管理相关类型定义
export interface StudentListParams {
  page?: number
  size?: number
  schoolId?: number
  classId?: number
  name?: string
}

export interface CreateStudentData {
  schoolId?: number
  name: string
  classId: number
  contactPhone?: string
  unpaidAmount?: number
}

export interface UpdateStudentData {
  name?: string
  classId?: number
  contactPhone?: string
  unpaidAmount?: number
}

export interface StudentInfo {
  studentId: number
  schoolId: number
  name: string
  classId: number
  contactPhone: string
  unpaidAmount: number
  createdAt: string
  updatedAt: string
  schoolClass?: {
    classId: number
    name: string
    gradeLevel: string
  }
  school?: {
    schoolId: number
    name: string
    address: string
  }
}

/**
 * 校长端学生管理API
 */
export const principalStudentApi = {
  /**
   * 获取学生列表（分页）
   */
  getList(params: StudentListParams): Promise<ApiResponse<PageResult<StudentInfo>>> {
    return request.get('/principal/students', params)
  },

  /**
   * 获取所有学生列表（不分页）
   */
  getAll(classId?: number): Promise<ApiResponse<StudentInfo[]>> {
    const params = classId ? { classId } : {}
    return request.get('/principal/students/all', params)
  },

  /**
   * 根据ID获取学生详情
   */
  getById(studentId: number): Promise<ApiResponse<StudentInfo>> {
    return request.get(`/principal/students/${studentId}`)
  },

  /**
   * 创建学生
   */
  create(data: CreateStudentData): Promise<ApiResponse<StudentInfo>> {
    return request.post('/principal/students', data)
  },

  /**
   * 更新学生信息
   */
  update(studentId: number, data: UpdateStudentData): Promise<ApiResponse<StudentInfo>> {
    return request.put(`/principal/students/${studentId}`, data)
  },

  /**
   * 删除学生
   */
  delete(studentId: number): Promise<ApiResponse<void>> {
    return request.delete(`/principal/students/${studentId}`)
  },

  /**
   * 根据多个ID获取学生列表
   */
  getByIds(studentIds: number[]): Promise<ApiResponse<StudentInfo[]>> {
    return request.post('/principal/students/by-ids', studentIds)
  }
}

/**
 * 管理员端学生管理API
 */
export const adminStudentApi = {
  /**
   * 获取学生列表（分页）
   */
  getList(params: StudentListParams): Promise<ApiResponse<PageResult<StudentInfo>>> {
    return request.get('/admin/students', params)
  },

  /**
   * 根据ID获取学生详情
   */
  getById(studentId: number): Promise<ApiResponse<StudentInfo>> {
    return request.get(`/admin/students/${studentId}`)
  },

  /**
   * 创建学生
   */
  create(data: CreateStudentData): Promise<ApiResponse<StudentInfo>> {
    return request.post('/admin/students', data)
  },

  /**
   * 更新学生信息
   */
  update(studentId: number, data: UpdateStudentData): Promise<ApiResponse<StudentInfo>> {
    return request.put(`/admin/students/${studentId}`, data)
  },

  /**
   * 删除学生
   */
  delete(studentId: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/students/${studentId}`)
  }
}

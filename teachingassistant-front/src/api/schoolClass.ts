import { request } from '@/utils/request'
import type { ApiResponse, PageResult } from '@/types'

// 班级管理相关类型定义
export interface SchoolClassListParams {
  page?: number
  size?: number
  schoolId?: number
  name?: string
}

export interface CreateSchoolClassData {
  schoolId?: number  // 管理员端需要，校长端自动设置
  name: string
  mainTeacher?: number
}

export interface UpdateSchoolClassData {
  name?: string
  mainTeacher?: number
}

export interface SchoolClassInfo {
  classId: number
  schoolId: number
  name: string
  mainTeacher?: number
  createdAt: string
  updatedAt: string
  mainTeacherInfo?: {
    userId: number
    realName: string
    username: string
  }
  school?: {
    schoolId: number
    name: string
    address?: string
  }
  studentCount?: number
  teacherCount?: number
}

/**
 * 管理员端班级管理API
 */
export const adminSchoolClassApi = {
  /**
   * 获取班级列表（分页）
   */
  getList(params: SchoolClassListParams): Promise<ApiResponse<PageResult<SchoolClassInfo>>> {
    return request.get('/admin/classes', params)
  },

  /**
   * 获取所有班级列表（不分页）
   */
  getAll(): Promise<ApiResponse<SchoolClassInfo[]>> {
    return request.get('/admin/classes/all')
  },

  /**
   * 根据ID获取班级详情
   */
  getById(classId: number): Promise<ApiResponse<SchoolClassInfo>> {
    return request.get(`/admin/classes/${classId}`)
  },

  /**
   * 创建班级
   */
  create(data: CreateSchoolClassData): Promise<ApiResponse<SchoolClassInfo>> {
    return request.post('/admin/classes', data)
  },

  /**
   * 更新班级信息
   */
  update(classId: number, data: UpdateSchoolClassData): Promise<ApiResponse<SchoolClassInfo>> {
    return request.put(`/admin/classes/${classId}`, data)
  },

  /**
   * 删除班级
   */
  delete(classId: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/classes/${classId}`)
  }
}

/**
 * 校长端班级管理API
 */
export const principalSchoolClassApi = {
  /**
   * 获取班级列表（分页）
   */
  getList(params: Omit<SchoolClassListParams, 'schoolId'>): Promise<ApiResponse<PageResult<SchoolClassInfo>>> {
    return request.get('/principal/classes', params)
  },

  /**
   * 获取所有班级列表（不分页）
   */
  getAll(): Promise<ApiResponse<SchoolClassInfo[]>> {
    return request.get('/principal/classes/all')
  },

  /**
   * 根据ID获取班级详情
   */
  getById(classId: number): Promise<ApiResponse<SchoolClassInfo>> {
    return request.get(`/principal/classes/${classId}`)
  },

  /**
   * 创建班级
   */
  create(data: Omit<CreateSchoolClassData, 'schoolId'>): Promise<ApiResponse<SchoolClassInfo>> {
    return request.post('/principal/classes', data)
  },

  /**
   * 更新班级信息
   */
  update(classId: number, data: UpdateSchoolClassData): Promise<ApiResponse<SchoolClassInfo>> {
    return request.put(`/principal/classes/${classId}`, data)
  },

  /**
   * 删除班级
   */
  delete(classId: number): Promise<ApiResponse<void>> {
    return request.delete(`/principal/classes/${classId}`)
  }
}

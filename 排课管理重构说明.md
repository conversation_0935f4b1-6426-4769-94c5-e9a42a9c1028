# 排课管理界面重构说明

## 重构概述

本次重构完全重新设计了排课管理界面，实现了类似Microsoft Teams日历的交互式排课功能，包含双视图模式（查看排课/新增排课），支持按教室、教师智能筛选，并实现空闲时段自动化计算。

## 核心功能特性

### 1. 双视图模式
- **查看排课视图**：显示现有课程安排，支持查看课程详情、编辑和删除
- **新增排课视图**：激活空闲时段计算，高亮显示可排课时间段，支持点击排课

### 2. 智能筛选系统（左侧侧边栏）
- **教室筛选器**：选择教室后显示该教室的所有排课
- **教师筛选器**：选择教师后显示该教师的授课计划
- **双条件筛选**：同时选中教室+教师，仅展示该教师在此教室的排课
- **侧边栏特性**：支持折叠/展开，默认展开状态

### 3. 类Teams日历视图
- **时空坐标系**：横轴为周一到周日，纵轴为00:00-24:00（30分钟刻度）
- **当前时间线**：红色横条动态标记当前时间点
- **周次导航**：支持查看过去和未来的周次数据
- **响应式设计**：适配不同屏幕尺寸

### 4. 交互式排课功能
- **空闲时段计算**：根据教室和教师的空闲时间计算重叠部分
- **可视化标记**：空闲区域用半透明绿色高亮显示
- **点击排课**：点击空闲时段弹出排课表单
- **多学生支持**：支持为一个时间段选择多个学生

### 5. 课程管理功能
- **课程创建**：支持设置时间、教师、教室、学生、价格等信息
- **课程查看**：详细的课程信息展示
- **课程编辑**：修改已排课程的信息
- **课程删除**：删除不需要的课程
- **状态管理**：支持已排课、已完成、已取消等状态

## 技术实现

### 前端重构
- **文件位置**：`teachingassistant-front/src/views/principal/schedule/index.vue`
- **主要技术**：Vue 3 Composition API、Element Plus、响应式设计
- **新增API**：课程管理API、学生管理API

### 后端新增
1. **CourseController**：课程管理控制器
2. **CourseService & CourseServiceImpl**：课程业务逻辑
3. **CourseMapper & CourseMapper.xml**：课程数据访问层
4. **StudentController**：学生管理控制器
5. **StudentService & StudentServiceImpl**：学生业务逻辑
6. **StudentMapper & StudentMapper.xml**：学生数据访问层

### 新增API接口
- **课程管理**：创建、查询、更新、删除课程
- **空闲时段查询**：计算教师和教室的空闲时间重叠
- **学生管理**：查询学生列表，支持多学生选择
- **教师管理**：获取教师列表
- **教室管理**：获取教室列表

## 使用说明

### 1. 查看排课
1. 在左侧侧边栏选择"查看排课"模式
2. 选择要查看的教室和/或教师
3. 选择周次查看对应时间段的课程
4. 点击课程块查看详细信息

### 2. 新增排课
1. 在左侧侧边栏选择"新增排课"模式
2. 选择教室和教师（必选）
3. 系统自动计算并高亮显示空闲时段
4. 点击绿色高亮区域打开排课表单
5. 填写课程信息：
   - 时间段（可调整，但不能超出空闲范围）
   - 学生（支持多选）
   - 课程价格
   - 年级信息
6. 点击"确定排课"完成排课

### 3. 课程管理
- **查看详情**：点击课程块查看完整信息
- **编辑课程**：在课程详情对话框中点击"编辑"
- **删除课程**：在课程详情对话框中点击"删除"
- **状态管理**：系统自动管理课程状态

## 数据库变更

### 新增表结构
- **courses**：课程排期表（已存在，新增了相关业务逻辑）
- **students**：学生信息表（已存在，新增了相关业务逻辑）

### 主要字段
- **courses表**：course_id, school_id, teacher_id, student_id, classroom_id, course_date, start_time, end_time, price, grade_level, status
- **students表**：student_id, school_id, name, class_id, contact_phone, unpaid_amount

## API文档

已生成完整的API文档文件：`排课管理API接口文档.yaml`

该文档包含：
- 课程管理相关接口
- 学生管理相关接口
- 教师管理相关接口
- 教室管理相关接口
- 完整的请求/响应模型定义

可直接导入到Apifox进行接口测试和文档管理。

## 部署说明

### 前端部署
1. 确保已安装所有依赖
2. 重新构建前端项目
3. 部署到Web服务器

### 后端部署
1. 确保数据库表结构已更新
2. 重新编译后端项目
3. 重启应用服务器

### 注意事项
- 确保数据库中已有基础数据（学校、教室、教师、学生）
- 检查权限配置，确保校长角色可以访问相关接口
- 建议在测试环境先验证功能完整性

## 功能演示

### 界面展示
- 左侧侧边栏：教室/教师筛选器、视图切换按钮
- 右侧日历视图：类Teams的周视图，30分钟时间刻度
- 空闲时段：绿色高亮显示可排课时间
- 课程块：蓝色渐变显示已排课程
- 当前时间线：红色线条标记当前时间

### 交互流程
1. 选择教室和教师
2. 切换到新增排课模式
3. 系统计算并显示空闲时段
4. 点击空闲区域进行排课
5. 填写课程信息并提交
6. 实时刷新显示新排课程

## 技术特点

- **响应式设计**：适配桌面和移动设备
- **实时计算**：动态计算空闲时段
- **可视化交互**：直观的时间轴和课程块
- **数据同步**：实时更新课程数据
- **权限控制**：基于角色的访问控制
- **错误处理**：完善的异常处理机制

## 后续优化建议

1. **智能排课算法**：实现自动排课功能
2. **冲突检测**：更智能的时间冲突检测
3. **批量操作**：支持批量创建和管理课程
4. **数据导出**：支持课表导出功能
5. **通知系统**：排课变更通知功能
6. **移动端优化**：进一步优化移动端体验

本次重构大幅提升了排课管理的用户体验和功能完整性，为后续功能扩展奠定了良好基础。

openapi: 3.0.3
info:
  title: 班级管理API
  description: 助教排课系统班级管理相关接口文档
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境

tags:
  - name: 管理员端班级管理
    description: 管理员端班级管理相关接口
  - name: 校长端班级管理
    description: 校长端班级管理相关接口
  - name: 老师端班级管理
    description: 老师端班级管理相关接口

paths:
  # 管理员端接口
  /admin/classes:
    get:
      tags:
        - 管理员端班级管理
      summary: 获取班级列表（分页）
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          description: 每页大小
          schema:
            type: integer
            default: 10
        - name: schoolId
          in: query
          description: 学校ID
          schema:
            type: integer
        - name: name
          in: query
          description: 班级名称
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResultSchoolClass'
    post:
      tags:
        - 管理员端班级管理
      summary: 创建班级
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateClassRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'

  /admin/classes/all:
    get:
      tags:
        - 管理员端班级管理
      summary: 获取所有班级列表（不分页）
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClassList'

  /admin/classes/{classId}:
    get:
      tags:
        - 管理员端班级管理
      summary: 根据ID获取班级详情
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'
    put:
      tags:
        - 管理员端班级管理
      summary: 更新班级信息
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateClassRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'
    delete:
      tags:
        - 管理员端班级管理
      summary: 删除班级
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultVoid'

  # 校长端接口
  /principal/classes:
    get:
      tags:
        - 校长端班级管理
      summary: 获取班级列表（分页）
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          description: 每页大小
          schema:
            type: integer
            default: 10
        - name: name
          in: query
          description: 班级名称
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResultSchoolClass'
    post:
      tags:
        - 校长端班级管理
      summary: 创建班级（自动归属到当前校长的学校）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateClassRequestPrincipal'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'

  /principal/classes/all:
    get:
      tags:
        - 校长端班级管理
      summary: 获取所有班级列表（不分页）
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClassList'

  /principal/classes/{classId}:
    get:
      tags:
        - 校长端班级管理
      summary: 根据ID获取班级详情
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'
    put:
      tags:
        - 校长端班级管理
      summary: 更新班级信息
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateClassRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'
    delete:
      tags:
        - 校长端班级管理
      summary: 删除班级
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultVoid'

  # 老师端接口
  /teacher/classes:
    get:
      tags:
        - 老师端班级管理
      summary: 获取班级列表（分页）
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          description: 每页大小
          schema:
            type: integer
            default: 10
        - name: name
          in: query
          description: 班级名称
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResultSchoolClass'
    post:
      tags:
        - 老师端班级管理
      summary: 创建班级（自动归属到老师所属校长的学校）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateClassRequestPrincipal'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'

  /teacher/classes/all:
    get:
      tags:
        - 老师端班级管理
      summary: 获取所有班级列表（不分页）
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClassList'

  /teacher/classes/{classId}:
    get:
      tags:
        - 老师端班级管理
      summary: 根据ID获取班级详情
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'
    put:
      tags:
        - 老师端班级管理
      summary: 更新班级信息
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateClassRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultSchoolClass'
    delete:
      tags:
        - 老师端班级管理
      summary: 删除班级
      parameters:
        - name: classId
          in: path
          required: true
          description: 班级ID
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultVoid'

  # 老师端获取老师列表接口
  /teacher/teachers/all:
    get:
      tags:
        - 老师端班级管理
      summary: 获取同校所有老师列表（不分页）
      description: 老师端获取同校所有老师列表，用于选择班主任
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultTeacherList'

components:
  schemas:
    SchoolClass:
      type: object
      properties:
        classId:
          type: integer
          description: 班级ID
        schoolId:
          type: integer
          description: 学校ID
        name:
          type: string
          description: 班级名称
        mainTeacher:
          type: integer
          description: 班主任ID
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
        mainTeacherInfo:
          $ref: '#/components/schemas/UserInfo'
        school:
          $ref: '#/components/schemas/SchoolInfo'
        studentCount:
          type: integer
          description: 学生数量
        teacherCount:
          type: integer
          description: 老师数量

    UserInfo:
      type: object
      properties:
        userId:
          type: integer
          description: 用户ID
        realName:
          type: string
          description: 真实姓名
        username:
          type: string
          description: 用户名

    SchoolInfo:
      type: object
      properties:
        schoolId:
          type: integer
          description: 学校ID
        name:
          type: string
          description: 学校名称
        address:
          type: string
          description: 学校地址

    CreateClassRequest:
      type: object
      required:
        - schoolId
        - name
      properties:
        schoolId:
          type: integer
          description: 学校ID
        name:
          type: string
          description: 班级名称
        mainTeacher:
          type: integer
          description: 班主任ID

    CreateClassRequestPrincipal:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          description: 班级名称
        mainTeacher:
          type: integer
          description: 班主任ID

    UpdateClassRequest:
      type: object
      properties:
        name:
          type: string
          description: 班级名称
        mainTeacher:
          type: integer
          description: 班主任ID

    PageResult:
      type: object
      properties:
        page:
          type: integer
          description: 当前页码
        size:
          type: integer
          description: 每页大小
        total:
          type: integer
          description: 总记录数
        records:
          type: array
          description: 数据列表

    PageResultSchoolClass:
      allOf:
        - $ref: '#/components/schemas/PageResult'
        - type: object
          properties:
            records:
              type: array
              items:
                $ref: '#/components/schemas/SchoolClass'

    Result:
      type: object
      properties:
        code:
          type: integer
          description: 响应码
        message:
          type: string
          description: 响应消息
        data:
          description: 响应数据
        timestamp:
          type: string
          format: date-time
          description: 响应时间

    ResultSchoolClass:
      allOf:
        - $ref: '#/components/schemas/Result'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/SchoolClass'

    ResultSchoolClassList:
      allOf:
        - $ref: '#/components/schemas/Result'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/SchoolClass'

    ResultVoid:
      allOf:
        - $ref: '#/components/schemas/Result'
        - type: object
          properties:
            data:
              type: object
              nullable: true

    Teacher:
      type: object
      properties:
        userId:
          type: integer
          description: 用户ID
        username:
          type: string
          description: 用户名
        realName:
          type: string
          description: 真实姓名
        role:
          type: string
          description: 角色
        schoolId:
          type: integer
          description: 学校ID
        schoolName:
          type: string
          description: 学校名称
        principalId:
          type: integer
          description: 校长ID
        principalName:
          type: string
          description: 校长姓名
        phone:
          type: string
          description: 手机号
        email:
          type: string
          description: 邮箱
        gender:
          type: string
          enum: [M, F]
          description: 性别
        hireDate:
          type: string
          format: date
          description: 入职日期
        subject:
          type: string
          description: 任教科目
        experience:
          type: integer
          description: 工作经验（年）
        bio:
          type: string
          description: 个人简介
        status:
          type: string
          description: 状态
        lastLoginAt:
          type: string
          format: date-time
          description: 最后登录时间
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    ResultTeacherList:
      allOf:
        - $ref: '#/components/schemas/Result'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Teacher'

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

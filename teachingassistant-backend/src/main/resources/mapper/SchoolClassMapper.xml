<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teachingassistant.mapper.SchoolClassMapper">

    <!-- 结果映射 -->
    <resultMap id="SchoolClassResultMap" type="com.teachingassistant.entity.SchoolClass">
        <id property="classId" column="class_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="name" column="name"/>
        <result property="mainTeacher" column="main_teacher"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <!-- 关联查询 -->
        <association property="mainTeacherInfo" javaType="com.teachingassistant.entity.User">
            <id property="userId" column="teacher_user_id"/>
            <result property="realName" column="teacher_real_name"/>
            <result property="username" column="teacher_username"/>
        </association>
        <association property="school" javaType="com.teachingassistant.entity.School">
            <id property="schoolId" column="school_school_id"/>
            <result property="name" column="school_name"/>
            <result property="address" column="school_address"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        c.class_id, c.school_id, c.name, c.main_teacher, c.created_at, c.updated_at
    </sql>

    <!-- 关联查询字段 -->
    <sql id="JoinColumns">
        <include refid="BaseColumns"/>,
        t.user_id as teacher_user_id, t.real_name as teacher_real_name, t.username as teacher_username,
        s.school_id as school_school_id, s.name as school_name, s.address as school_address
    </sql>

    <!-- 根据班级ID查询班级 -->
    <select id="findById" resultMap="SchoolClassResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM classes c
        LEFT JOIN users t ON c.main_teacher = t.user_id
        LEFT JOIN schools s ON c.school_id = s.school_id
        WHERE c.class_id = #{classId}
    </select>

    <!-- 根据学校ID查询班级列表 -->
    <select id="findBySchoolId" resultMap="SchoolClassResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM classes c
        LEFT JOIN users t ON c.main_teacher = t.user_id
        LEFT JOIN schools s ON c.school_id = s.school_id
        WHERE c.school_id = #{schoolId}
        ORDER BY c.created_at DESC
    </select>

    <!-- 查询所有班级列表 -->
    <select id="findAll" resultMap="SchoolClassResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM classes c
        LEFT JOIN users t ON c.main_teacher = t.user_id
        LEFT JOIN schools s ON c.school_id = s.school_id
        ORDER BY c.created_at DESC
    </select>

    <!-- 分页查询班级列表（管理员端） -->
    <select id="findWithPagination" resultMap="SchoolClassResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM classes c
        LEFT JOIN users t ON c.main_teacher = t.user_id
        LEFT JOIN schools s ON c.school_id = s.school_id
        <where>
            <if test="schoolId != null">
                AND c.school_id = #{schoolId}
            </if>
            <if test="name != null and name != ''">
                AND c.name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
        ORDER BY c.created_at DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 分页查询班级列表（校长端） -->
    <select id="findBySchoolWithPagination" resultMap="SchoolClassResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM classes c
        LEFT JOIN users t ON c.main_teacher = t.user_id
        LEFT JOIN schools s ON c.school_id = s.school_id
        WHERE c.school_id = #{schoolId}
        <if test="name != null and name != ''">
            AND c.name LIKE CONCAT('%', #{name}, '%')
        </if>
        ORDER BY c.created_at DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 统计班级总数（管理员端） -->
    <select id="countClasses" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM classes c
        <where>
            <if test="schoolId != null">
                AND c.school_id = #{schoolId}
            </if>
            <if test="name != null and name != ''">
                AND c.name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
    </select>

    <!-- 统计班级总数（校长端） -->
    <select id="countBySchool" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM classes c
        WHERE c.school_id = #{schoolId}
        <if test="name != null and name != ''">
            AND c.name LIKE CONCAT('%', #{name}, '%')
        </if>
    </select>

    <!-- 插入班级 -->
    <insert id="insert" parameterType="com.teachingassistant.entity.SchoolClass" useGeneratedKeys="true" keyProperty="classId">
        INSERT INTO classes (school_id, name, main_teacher, created_at, updated_at)
        VALUES (#{schoolId}, #{name}, #{mainTeacher}, NOW(), NOW())
    </insert>

    <!-- 更新班级信息 -->
    <update id="update" parameterType="com.teachingassistant.entity.SchoolClass">
        UPDATE classes
        SET name = #{name},
            main_teacher = #{mainTeacher},
            updated_at = NOW()
        WHERE class_id = #{classId}
    </update>

    <!-- 根据ID删除班级 -->
    <delete id="deleteById">
        DELETE FROM classes WHERE class_id = #{classId}
    </delete>

    <!-- 检查班级名称是否在同一学校内存在 -->
    <select id="existsByNameAndSchoolId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM classes
        WHERE name = #{name} AND school_id = #{schoolId}
    </select>

    <!-- 检查班级名称是否在同一学校内存在（排除指定ID） -->
    <select id="existsByNameAndSchoolIdExcludeId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM classes
        WHERE name = #{name} AND school_id = #{schoolId} AND class_id != #{classId}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teachingassistant.mapper.CourseMapper">

    <!-- 结果映射 -->
    <resultMap id="CourseResultMap" type="com.teachingassistant.entity.Course">
        <id property="courseId" column="course_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="studentId" column="student_id"/>
        <result property="classroomId" column="classroom_id"/>
        <result property="courseDate" column="course_date"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="price" column="price"/>
        <result property="gradeLevel" column="grade_level"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <!-- 关联查询 -->
        <association property="teacher" javaType="com.teachingassistant.entity.User">
            <id property="userId" column="teacher_user_id"/>
            <result property="realName" column="teacher_real_name"/>
            <result property="phone" column="teacher_phone"/>
            <result property="email" column="teacher_email"/>
        </association>
        <association property="student" javaType="com.teachingassistant.entity.Student">
            <id property="studentId" column="student_student_id"/>
            <result property="name" column="student_name"/>
            <result property="contactPhone" column="student_contact_phone"/>
        </association>
        <association property="classroom" javaType="com.teachingassistant.entity.Classroom">
            <id property="classroomId" column="classroom_classroom_id"/>
            <result property="name" column="classroom_name"/>
            <result property="floor" column="classroom_floor"/>
            <result property="type" column="classroom_type"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        c.course_id, c.school_id, c.teacher_id, c.student_id, c.classroom_id,
        c.course_date, c.start_time, c.end_time, c.price, c.grade_level,
        c.status, c.created_at, c.updated_at
    </sql>

    <!-- 关联查询字段 -->
    <sql id="JoinColumns">
        <include refid="BaseColumns"/>,
        t.user_id as teacher_user_id, t.real_name as teacher_real_name,
        t.phone as teacher_phone, t.email as teacher_email,
        s.student_id as student_student_id, s.name as student_name,
        s.contact_phone as student_contact_phone,
        cr.classroom_id as classroom_classroom_id, cr.name as classroom_name,
        cr.floor as classroom_floor, cr.type as classroom_type
    </sql>

    <!-- 基础表连接 -->
    <sql id="BaseJoins">
        LEFT JOIN users t ON c.teacher_id = t.user_id
        LEFT JOIN students s ON c.student_id = s.student_id
        LEFT JOIN classrooms cr ON c.classroom_id = cr.classroom_id
    </sql>

    <!-- 查询条件 -->
    <sql id="WhereConditions">
        <where>
            <if test="schoolId != null">
                AND c.school_id = #{schoolId}
            </if>
            <if test="teacherId != null">
                AND c.teacher_id = #{teacherId}
            </if>
            <if test="classroomId != null">
                AND c.classroom_id = #{classroomId}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
        </where>
    </sql>

    <!-- 根据课程ID查询课程 -->
    <select id="findById" resultMap="CourseResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM courses c
        <include refid="BaseJoins"/>
        WHERE c.course_id = #{courseId}
    </select>

    <!-- 根据学校ID查询课程列表 -->
    <select id="findBySchoolId" resultMap="CourseResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM courses c
        <include refid="BaseJoins"/>
        WHERE c.school_id = #{schoolId}
        ORDER BY c.course_date DESC, c.start_time ASC
    </select>

    <!-- 查询指定日期范围内的课程 -->
    <select id="findByDateRange" resultMap="CourseResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM courses c
        <include refid="BaseJoins"/>
        WHERE c.school_id = #{schoolId}
        AND c.course_date BETWEEN #{startDate} AND #{endDate}
        <if test="teacherId != null">
            AND c.teacher_id = #{teacherId}
        </if>
        <if test="classroomId != null">
            AND c.classroom_id = #{classroomId}
        </if>
        ORDER BY c.course_date ASC, c.start_time ASC
    </select>

    <!-- 查询教师在指定日期的课程 -->
    <select id="findByTeacherAndDate" resultMap="CourseResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM courses c
        WHERE c.teacher_id = #{teacherId}
        AND c.course_date = #{courseDate}
        ORDER BY c.start_time ASC
    </select>

    <!-- 查询教室在指定日期的课程 -->
    <select id="findByClassroomAndDate" resultMap="CourseResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM courses c
        WHERE c.classroom_id = #{classroomId}
        AND c.course_date = #{courseDate}
        ORDER BY c.start_time ASC
    </select>

    <!-- 查询教师在指定日期范围内的课程 -->
    <select id="findByTeacherAndDateRange" resultMap="CourseResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM courses c
        WHERE c.teacher_id = #{teacherId}
        AND c.course_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY c.course_date ASC, c.start_time ASC
    </select>

    <!-- 查询教室在指定日期范围内的课程 -->
    <select id="findByClassroomAndDateRange" resultMap="CourseResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM courses c
        WHERE c.classroom_id = #{classroomId}
        AND c.course_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY c.course_date ASC, c.start_time ASC
    </select>

    <!-- 检查时间冲突 -->
    <select id="findConflictCourses" resultMap="CourseResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM courses c
        WHERE c.course_date = #{courseDate}
        AND (c.teacher_id = #{teacherId} OR c.classroom_id = #{classroomId})
        AND (
            (c.start_time &lt; #{endTime} AND c.end_time &gt; #{startTime})
        )
        <if test="excludeCourseId != null">
            AND c.course_id != #{excludeCourseId}
        </if>
    </select>

    <!-- 分页查询课程列表 -->
    <select id="findWithPagination" resultMap="CourseResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM courses c
        <include refid="BaseJoins"/>
        <include refid="WhereConditions"/>
        ORDER BY c.course_date DESC, c.start_time ASC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 统计课程总数 -->
    <select id="countCourses" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM courses c
        <include refid="WhereConditions"/>
    </select>

    <!-- 插入课程 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="courseId">
        INSERT INTO courses (
            school_id, teacher_id, student_id, classroom_id,
            course_date, start_time, end_time, price, grade_level, status
        ) VALUES (
            #{schoolId}, #{teacherId}, #{studentId}, #{classroomId},
            #{courseDate}, #{startTime}, #{endTime}, #{price}, #{gradeLevel}, #{status}
        )
    </insert>

    <!-- 更新课程信息 -->
    <update id="update">
        UPDATE courses SET
            teacher_id = #{teacherId},
            student_id = #{studentId},
            classroom_id = #{classroomId},
            course_date = #{courseDate},
            start_time = #{startTime},
            end_time = #{endTime},
            price = #{price},
            grade_level = #{gradeLevel},
            status = #{status},
            updated_at = CURRENT_TIMESTAMP
        WHERE course_id = #{courseId}
    </update>

    <!-- 删除课程 -->
    <delete id="deleteById">
        DELETE FROM courses WHERE course_id = #{courseId}
    </delete>

    <!-- 批量删除课程 -->
    <delete id="deleteByIds">
        DELETE FROM courses WHERE course_id IN
        <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>

    <!-- 更新课程状态 -->
    <update id="updateStatus">
        UPDATE courses SET
            status = #{status},
            updated_at = CURRENT_TIMESTAMP
        WHERE course_id = #{courseId}
    </update>

</mapper>

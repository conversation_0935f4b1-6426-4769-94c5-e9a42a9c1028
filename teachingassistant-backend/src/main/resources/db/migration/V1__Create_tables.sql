-- 助教排课系统建表语句
-- 使用MySQL 8.0语法

-- ----------------------------
-- 1. 学校信息表
-- ----------------------------
CREATE TABLE `schools` (
  `school_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '学校ID',
  `name` VARCHAR(100) NOT NULL COMMENT '学校名称',
  `address` VARCHAR(200) COMMENT '学校地址',
  `phone` VARCHAR(20) COMMENT '联系电话',
  `email` VARCHAR(100) COMMENT '邮箱',
  `admin_quota` INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '管理员配额',
  `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '学校状态',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='学校信息表';

-- ----------------------------
-- 2. 用户信息表（多角色）
-- ----------------------------
CREATE TABLE `users` (
  `user_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
  `school_id` BIGINT UNSIGNED COMMENT '所属学校ID',
  `principal_id` BIGINT UNSIGNED NULL COMMENT '所属校长ID（仅老师角色使用）',
  `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '登录账号',
  `password` VARCHAR(100) NOT NULL COMMENT '加密密码',
  `role` ENUM('super_admin', 'principal', 'teacher') NOT NULL COMMENT '用户角色',
  `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `phone` VARCHAR(20) COMMENT '绑定手机',
  `email` VARCHAR(100) COMMENT '邮箱地址',
  `gender` ENUM('M', 'F') COMMENT '性别：M-男，F-女',
  `hire_date` DATE COMMENT '入职时间',
  `subject` VARCHAR(200) COMMENT '主教科目（教师角色使用，多个科目用逗号分隔，如：数学,物理,化学）',
  `experience` INT UNSIGNED COMMENT '教学经验年数（教师角色使用）',
  `bio` TEXT COMMENT '个人简介',
  `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间',
  `mfa_secret` VARCHAR(50) COMMENT 'MFA设备密钥',
  `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '用户状态',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`school_id`) REFERENCES `schools`(`school_id`),
  CONSTRAINT `fk_users_principal` FOREIGN KEY (`principal_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
  UNIQUE INDEX `idx_users_email` (`email`),
  INDEX `idx_users_hire_date` (`hire_date`),
  INDEX `idx_users_subject` (`subject`),
  INDEX `idx_users_principal_id` (`principal_id`)
) ENGINE=InnoDB COMMENT='系统用户表';

-- ----------------------------
-- 3. 班级信息表
-- ----------------------------
CREATE TABLE `classes` (
  `class_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '班级ID',
  `school_id` BIGINT UNSIGNED NOT NULL COMMENT '所属学校ID',
  `name` VARCHAR(50) NOT NULL COMMENT '班级名称',
  `main_teacher` BIGINT UNSIGNED COMMENT '班主任',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`school_id`) REFERENCES `schools`(`school_id`),
  FOREIGN KEY (`main_teacher`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='班级信息表';

-- ----------------------------
-- 4. 学生信息表
-- ----------------------------
CREATE TABLE `students` (
  `student_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '学生ID',
  `school_id` BIGINT UNSIGNED NOT NULL COMMENT '所属学校ID',
  `name` VARCHAR(50) NOT NULL COMMENT '学生姓名',
  `class_id` BIGINT UNSIGNED COMMENT '班级ID',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `unpaid_amount` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '未付金额',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`school_id`) REFERENCES `schools`(`school_id`),
  FOREIGN KEY (`class_id`) REFERENCES `classes`(`class_id`)
) ENGINE=InnoDB COMMENT='学生信息表';

-- ----------------------------
-- 5. 教室信息表
-- ----------------------------
CREATE TABLE `classrooms` (
  `classroom_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '教室ID',
  `school_id` BIGINT UNSIGNED NOT NULL COMMENT '所属学校ID',
  `name` VARCHAR(50) NOT NULL COMMENT '教室名称',
  `floor` INT COMMENT '楼层',
  `type` ENUM('normal', 'multimedia', 'lab') NOT NULL DEFAULT 'normal' COMMENT '教室类型：normal-普通教室，multimedia-多媒体教室，lab-实验室',
  `capacity` INT UNSIGNED DEFAULT 30 COMMENT '容量（人数）',
  `equipment` TEXT COMMENT '设备描述',
  `status` ENUM('available', 'in_use', 'maintenance') NOT NULL DEFAULT 'available' COMMENT '教室状态',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`school_id`) REFERENCES `schools`(`school_id`)
) ENGINE=InnoDB COMMENT='教室资源表';

-- ----------------------------
-- 6. 课程信息表（排课核心表）
-- ----------------------------
CREATE TABLE `courses` (
  `course_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '课程ID',
  `school_id` BIGINT UNSIGNED NOT NULL COMMENT '所属学校ID',
  `teacher_id` BIGINT UNSIGNED NOT NULL COMMENT '授课老师ID',
  `student_id` BIGINT UNSIGNED NOT NULL COMMENT '学生ID',
  `classroom_id` BIGINT UNSIGNED NOT NULL COMMENT '教室ID',
  `course_date` DATE NOT NULL COMMENT '上课日期',
  `start_time` TIME NOT NULL COMMENT '开始时间',
  `end_time` TIME NOT NULL COMMENT '结束时间',
  `price` DECIMAL(8,2) NOT NULL COMMENT '课程价格',
  `grade_level` VARCHAR(20) NOT NULL COMMENT '年级',
  `status` ENUM('scheduled', 'completed', 'cancelled') NOT NULL DEFAULT 'scheduled' COMMENT '课程状态',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`school_id`) REFERENCES `schools`(`school_id`),
  FOREIGN KEY (`teacher_id`) REFERENCES `users`(`user_id`),
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`),
  FOREIGN KEY (`classroom_id`) REFERENCES `classrooms`(`classroom_id`),
  INDEX `idx_time` (`course_date`, `start_time`, `end_time`),
  INDEX `idx_teacher_date` (`teacher_id`, `course_date`),
  INDEX `idx_classroom_date` (`classroom_id`, `course_date`)
) ENGINE=InnoDB COMMENT='课程排期表';

-- ----------------------------
-- 7. 考勤记录表
-- ----------------------------
CREATE TABLE `attendances` (
  `attendance_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '考勤ID',
  `course_id` BIGINT UNSIGNED NOT NULL COMMENT '课程ID',
  `student_id` BIGINT UNSIGNED NOT NULL COMMENT '学生ID',
  `status` ENUM('present', 'absent', 'late') NOT NULL COMMENT '出勤状态',
  `recorded_by` BIGINT UNSIGNED COMMENT '记录人',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`course_id`) REFERENCES `courses`(`course_id`),
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`),
  FOREIGN KEY (`recorded_by`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='学生考勤表';

-- ----------------------------
-- 8. 工资明细表
-- ----------------------------
CREATE TABLE `salaries` (
  `salary_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '工资单ID',
  `teacher_id` BIGINT UNSIGNED NOT NULL COMMENT '老师ID',
  `period` DATE NOT NULL COMMENT '薪资周期',
  `base_amount` DECIMAL(10,2) NOT NULL COMMENT '基本课时费',
  `bonus` DECIMAL(8,2) DEFAULT 0 COMMENT '绩效奖金',
  `deductions` DECIMAL(8,2) DEFAULT 0 COMMENT '扣款项',
  `total_amount` DECIMAL(10,2) NOT NULL COMMENT '实发金额',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`teacher_id`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='教师工资表';

-- ----------------------------
-- 9. 财务缴费表
-- ----------------------------
CREATE TABLE `payments` (
  `payment_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '缴费ID',
  `student_id` BIGINT UNSIGNED NOT NULL COMMENT '学生ID',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '缴费金额',
  `payment_date` DATE NOT NULL COMMENT '缴费日期',
  `school_year` VARCHAR(20) COMMENT '所属学年',
  `created_by` BIGINT UNSIGNED COMMENT '操作人',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`),
  FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='学生缴费表';

-- ----------------------------
-- 10. 消息通知表
-- ----------------------------
CREATE TABLE `messages` (
  `message_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
  `school_id` BIGINT UNSIGNED NOT NULL COMMENT '学校ID',
  `sender_id` BIGINT UNSIGNED COMMENT '发送人',
  `receiver_id` BIGINT UNSIGNED NOT NULL COMMENT '接收人',
  `type` ENUM('booking', 'notification', 'payment') NOT NULL COMMENT '消息类型',
  `content` TEXT NOT NULL COMMENT '消息内容',
  `status` ENUM('unread', 'read', 'confirmed') NOT NULL DEFAULT 'unread' COMMENT '消息状态',
  `priority` ENUM('normal', 'urgent') NOT NULL DEFAULT 'normal' COMMENT '优先级',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`school_id`) REFERENCES `schools`(`school_id`),
  FOREIGN KEY (`sender_id`) REFERENCES `users`(`user_id`),
  FOREIGN KEY (`receiver_id`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='消息中心表';

-- ----------------------------
-- 11. 操作日志表
-- ----------------------------
CREATE TABLE `operation_logs` (
  `log_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '操作用户',
  `operation` VARCHAR(100) NOT NULL COMMENT '操作内容',
  `ip_address` VARCHAR(45) COMMENT '操作IP',
  `result` ENUM('success', 'failure') NOT NULL COMMENT '操作结果',
  `details` TEXT COMMENT '详细信息',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_user_operation` (`user_id`, `operation`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='操作日志表';

-- ----------------------------
-- 12. 老师-学生关联表
-- ----------------------------
CREATE TABLE `teacher_student` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `teacher_id` BIGINT UNSIGNED NOT NULL COMMENT '老师ID',
  `student_id` BIGINT UNSIGNED NOT NULL COMMENT '学生ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_relation` (`teacher_id`, `student_id`),
  FOREIGN KEY (`teacher_id`) REFERENCES `users`(`user_id`),
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`)
) ENGINE=InnoDB COMMENT='师生关联表';

-- ----------------------------
-- 13. 约课信息表
-- ----------------------------
CREATE TABLE `bookings` (
  `booking_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '约课ID',
  `school_id` BIGINT UNSIGNED NOT NULL COMMENT '学校ID',
  `student_id` BIGINT UNSIGNED NOT NULL COMMENT '学生ID',
  `teacher_id` BIGINT UNSIGNED COMMENT '指定老师ID',
  `classroom_id` BIGINT UNSIGNED COMMENT '指定教室ID',
  `preferred_date` DATE NOT NULL COMMENT '期望日期',
  `preferred_time` TIME NOT NULL COMMENT '期望时间',
  `duration` INT NOT NULL DEFAULT 90 COMMENT '课程时长(分钟)',
  `grade_level` VARCHAR(20) NOT NULL COMMENT '年级',
  `subject` VARCHAR(50) COMMENT '科目',
  `status` ENUM('pending', 'confirmed', 'rejected') NOT NULL DEFAULT 'pending' COMMENT '约课状态',
  `remarks` TEXT COMMENT '备注信息',
  `processed_by` BIGINT UNSIGNED COMMENT '处理人',
  `processed_at` TIMESTAMP NULL COMMENT '处理时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`school_id`) REFERENCES `schools`(`school_id`),
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`),
  FOREIGN KEY (`teacher_id`) REFERENCES `users`(`user_id`),
  FOREIGN KEY (`classroom_id`) REFERENCES `classrooms`(`classroom_id`),
  FOREIGN KEY (`processed_by`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='约课信息表';

-- ----------------------------
-- 14. 用户设置表
-- ----------------------------
CREATE TABLE `user_settings` (
  `setting_id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '设置ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `notification_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '消息提醒开关',
  `theme` ENUM('light', 'dark') NOT NULL DEFAULT 'light' COMMENT '界面主题',
  `language` VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言设置',
  `class_reminder` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '课程提醒开关',
  `appointment_reminder` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '约课提醒开关',
  `sidebar_collapsed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '侧边栏折叠状态',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_user_setting` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`)
) ENGINE=InnoDB COMMENT='用户设置表';

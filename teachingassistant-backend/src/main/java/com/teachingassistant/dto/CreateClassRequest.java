package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建班级请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class CreateClassRequest {
    
    /**
     * 学校ID（管理员端需要，校长端自动设置）
     */
    private Long schoolId;
    
    /**
     * 班级名称
     */
    @NotBlank(message = "班级名称不能为空")
    private String name;
    
    /**
     * 班主任ID
     */
    private Long mainTeacher;
}

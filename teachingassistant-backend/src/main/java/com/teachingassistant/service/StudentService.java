package com.teachingassistant.service;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.entity.Student;

import java.util.List;

/**
 * 学生服务接口
 *
 * <AUTHOR> Assistant System
 */
public interface StudentService {

    /**
     * 根据学生ID查询学生
     */
    Student findById(Long studentId);

    /**
     * 根据学校ID查询学生列表
     */
    List<Student> findBySchoolId(Long schoolId);

    /**
     * 根据班级ID查询学生列表
     */
    List<Student> findByClassId(Long classId);

    /**
     * 分页查询学生列表
     */
    PageResult<Student> findWithPagination(Integer page, Integer size, Long schoolId, 
                                          Long classId, String name);

    /**
     * 创建学生
     */
    Student createStudent(Student student);

    /**
     * 更新学生信息
     */
    Student updateStudent(Student student);

    /**
     * 删除学生
     */
    void deleteStudent(Long studentId);

    /**
     * 检查学生姓名是否在同一班级内存在
     */
    boolean existsByNameAndClassId(String name, Long classId);

    /**
     * 检查学生姓名是否在同一班级内存在（排除指定ID）
     */
    boolean existsByNameAndClassIdExcludeId(String name, Long classId, Long studentId);

    /**
     * 根据多个学生ID查询学生列表
     */
    List<Student> findByIds(List<Long> studentIds);

    /**
     * 验证用户是否有权限访问指定学生
     */
    boolean hasPermissionToAccess(Long studentId, Long userSchoolId, String userRole);
}

package com.teachingassistant.service;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.entity.SchoolClass;

import java.util.List;

/**
 * 班级服务接口
 *
 * <AUTHOR> Assistant System
 */
public interface SchoolClassService {

    /**
     * 根据班级ID查询班级
     */
    SchoolClass findById(Long classId);

    /**
     * 根据学校ID查询班级列表
     */
    List<SchoolClass> findBySchoolId(Long schoolId);

    /**
     * 查询所有班级列表
     */
    List<SchoolClass> findAll();

    /**
     * 分页查询班级列表（管理员端）
     */
    PageResult<SchoolClass> findWithPagination(Integer page, Integer size, Long schoolId, String name);

    /**
     * 分页查询班级列表（校长端）
     */
    PageResult<SchoolClass> findBySchoolWithPagination(Long schoolId, Integer page, Integer size, String name);

    /**
     * 创建班级
     */
    SchoolClass createClass(SchoolClass schoolClass);

    /**
     * 更新班级信息
     */
    SchoolClass updateClass(SchoolClass schoolClass);

    /**
     * 删除班级
     */
    void deleteClass(Long classId);

    /**
     * 检查班级名称是否在同一学校内存在
     */
    boolean existsByNameAndSchoolId(String name, Long schoolId);

    /**
     * 检查班级名称是否在同一学校内存在（排除指定ID）
     */
    boolean existsByNameAndSchoolIdExcludeId(String name, Long schoolId, Long classId);

    /**
     * 检查用户是否有权限访问指定班级
     */
    boolean hasPermissionToAccess(Long classId, Long userSchoolId, String userRole);
}

package com.teachingassistant.service;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.entity.User;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Assistant System
 */
public interface UserService {
    
    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);
    
    /**
     * 根据用户ID查询用户
     */
    User findById(Long userId);
    
    /**
     * 根据学校ID查询用户列表
     */
    List<User> findBySchoolId(Long schoolId);
    
    /**
     * 根据角色查询用户列表
     */
    List<User> findByRole(String role);
    
    /**
     * 根据学校ID和角色查询用户列表
     */
    List<User> findBySchoolIdAndRole(Long schoolId, String role);
    
    /**
     * 分页查询用户列表
     */
    PageResult<User> findWithPagination(Integer page, Integer size, Long schoolId, String role, String realName);
    
    /**
     * 创建用户
     */
    User createUser(User user);
    
    /**
     * 更新用户信息
     */
    User updateUser(User user);
    
    /**
     * 更新用户密码
     */
    void updatePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 重置用户密码
     */
    void resetPassword(Long userId, String newPassword);
    
    /**
     * 更新用户状态
     */
    void updateStatus(Long userId, String status);
    
    /**
     * 删除用户
     */
    void deleteUser(Long userId);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 更新用户邮箱
     */
    void updateEmail(Long userId, String email);

    /**
     * 验证用户密码
     */
    boolean validatePassword(String rawPassword, String encodedPassword);

    /**
     * 编码密码
     */
    String encodePassword(String rawPassword);

    /**
     * 更新最后登录时间
     */
    void updateLastLoginTime(Long userId);

    /**
     * 分页查询未分配校长的老师列表
     */
    PageResult<User> findUnassignedTeachers(Integer page, Integer size, Long schoolId, String realName);

    /**
     * 分页查询老师列表（包含校长信息）
     */
    PageResult<User> findTeachersWithPrincipal(Integer page, Integer size, Long schoolId, Long principalId, String realName);

    /**
     * 批量分配老师给校长
     */
    void assignTeachersToPrincipal(Long principalId, List<Long> teacherIds);

    /**
     * 解除老师与校长的关系
     */
    void removeTeacherFromPrincipal(Long teacherId);

    /**
     * 根据校长ID查询老师列表
     */
    List<User> findTeachersByPrincipalId(Long principalId);
}

package com.teachingassistant.service.impl;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.SchoolClass;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.SchoolClassMapper;
import com.teachingassistant.service.SchoolClassService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 班级服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SchoolClassServiceImpl implements SchoolClassService {
    
    private final SchoolClassMapper schoolClassMapper;
    
    @Override
    public SchoolClass findById(Long classId) {
        if (classId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级ID不能为空");
        }
        SchoolClass schoolClass = schoolClassMapper.findById(classId);
        if (schoolClass == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "班级不存在");
        }
        return schoolClass;
    }
    
    @Override
    public List<SchoolClass> findBySchoolId(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        return schoolClassMapper.findBySchoolId(schoolId);
    }
    
    @Override
    public List<SchoolClass> findAll() {
        return schoolClassMapper.findAll();
    }

    @Override
    public PageResult<SchoolClass> findWithPagination(Integer page, Integer size, Long schoolId, String name) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        Integer offset = (page - 1) * size;
        List<SchoolClass> classes = schoolClassMapper.findWithPagination(offset, size, schoolId, name);
        Integer total = schoolClassMapper.countClasses(schoolId, name);

        return PageResult.of(page, size, total.longValue(), classes);
    }

    @Override
    public PageResult<SchoolClass> findBySchoolWithPagination(Long schoolId, Integer page, Integer size, String name) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        Integer offset = (page - 1) * size;
        List<SchoolClass> classes = schoolClassMapper.findBySchoolWithPagination(offset, size, schoolId, name);
        Integer total = schoolClassMapper.countBySchool(schoolId, name);

        return PageResult.of(page, size, total.longValue(), classes);
    }
    
    @Override
    @Transactional
    public SchoolClass createClass(SchoolClass schoolClass) {
        validateClassForCreate(schoolClass);
        
        // 检查班级名称是否在同一学校内已存在
        if (existsByNameAndSchoolId(schoolClass.getName(), schoolClass.getSchoolId())) {
            throw new BusinessException(ResultCode.DATA_EXISTS, "该学校内班级名称已存在");
        }
        
        int result = schoolClassMapper.insert(schoolClass);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "班级创建失败");
        }
        
        log.info("班级创建成功: {}", schoolClass.getName());
        return findById(schoolClass.getClassId());
    }
    
    @Override
    @Transactional
    public SchoolClass updateClass(SchoolClass schoolClass) {
        validateClassForUpdate(schoolClass);
        
        // 检查班级是否存在
        SchoolClass existingClass = findById(schoolClass.getClassId());
        
        // 检查班级名称是否在同一学校内存在（排除当前班级）
        if (existsByNameAndSchoolIdExcludeId(schoolClass.getName(), schoolClass.getSchoolId(), schoolClass.getClassId())) {
            throw new BusinessException(ResultCode.DATA_EXISTS, "该学校内班级名称已存在");
        }
        
        int result = schoolClassMapper.update(schoolClass);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "班级更新失败");
        }
        
        log.info("班级更新成功: ID={}", schoolClass.getClassId());
        return findById(schoolClass.getClassId());
    }
    
    @Override
    @Transactional
    public void deleteClass(Long classId) {
        if (classId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级ID不能为空");
        }
        
        // 检查班级是否存在
        SchoolClass schoolClass = findById(classId);
        
        // TODO: 检查班级是否有学生，如果有则不允许删除
        
        int result = schoolClassMapper.deleteById(classId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "班级删除失败");
        }
        
        log.info("班级删除成功: ID={}", classId);
    }
    
    @Override
    public boolean existsByNameAndSchoolId(String name, Long schoolId) {
        if (!StringUtils.hasText(name) || schoolId == null) {
            return false;
        }
        return schoolClassMapper.existsByNameAndSchoolId(name, schoolId);
    }
    
    @Override
    public boolean existsByNameAndSchoolIdExcludeId(String name, Long schoolId, Long classId) {
        if (!StringUtils.hasText(name) || schoolId == null || classId == null) {
            return false;
        }
        return schoolClassMapper.existsByNameAndSchoolIdExcludeId(name, schoolId, classId);
    }
    
    @Override
    public boolean hasPermissionToAccess(Long classId, Long userSchoolId, String userRole) {
        if (classId == null || userSchoolId == null || !StringUtils.hasText(userRole)) {
            return false;
        }
        
        SchoolClass schoolClass = schoolClassMapper.findById(classId);
        if (schoolClass == null) {
            return false;
        }
        
        // 超级管理员有所有权限
        if ("super_admin".equals(userRole)) {
            return true;
        }
        
        // 校长和老师只能访问本校的班级
        return schoolClass.getSchoolId().equals(userSchoolId);
    }
    
    /**
     * 验证班级创建参数
     */
    private void validateClassForCreate(SchoolClass schoolClass) {
        if (schoolClass == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级信息不能为空");
        }
        if (!StringUtils.hasText(schoolClass.getName())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级名称不能为空");
        }
        if (schoolClass.getSchoolId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
    }
    
    /**
     * 验证班级更新参数
     */
    private void validateClassForUpdate(SchoolClass schoolClass) {
        if (schoolClass == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级信息不能为空");
        }
        if (schoolClass.getClassId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级ID不能为空");
        }
        if (!StringUtils.hasText(schoolClass.getName())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级名称不能为空");
        }
        if (schoolClass.getSchoolId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
    }
}

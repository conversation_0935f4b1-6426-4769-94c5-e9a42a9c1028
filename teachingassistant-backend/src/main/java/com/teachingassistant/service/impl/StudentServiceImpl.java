package com.teachingassistant.service.impl;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.Student;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.StudentMapper;
import com.teachingassistant.service.StudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 学生服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StudentServiceImpl implements StudentService {
    
    private final StudentMapper studentMapper;
    
    @Override
    public Student findById(Long studentId) {
        if (studentId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生ID不能为空");
        }
        Student student = studentMapper.findById(studentId);
        if (student == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "学生不存在");
        }
        return student;
    }
    
    @Override
    public List<Student> findBySchoolId(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        return studentMapper.findBySchoolId(schoolId);
    }
    
    @Override
    public List<Student> findByClassId(Long classId) {
        if (classId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级ID不能为空");
        }
        return studentMapper.findByClassId(classId);
    }
    
    @Override
    public PageResult<Student> findWithPagination(Integer page, Integer size, Long schoolId, 
                                                 Long classId, String name) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        Integer offset = (page - 1) * size;
        List<Student> students = studentMapper.findWithPagination(offset, size, schoolId, classId, name);
        Integer total = studentMapper.countStudents(schoolId, classId, name);
        
        return PageResult.of(page, size, total.longValue(), students);
    }
    
    @Override
    @Transactional
    public Student createStudent(Student student) {
        validateStudentForCreate(student);
        
        // 检查学生姓名是否在同一班级内存在
        if (existsByNameAndClassId(student.getName(), student.getClassId())) {
            throw new BusinessException(ResultCode.DATA_EXISTS, "该班级内已存在同名学生");
        }
        
        int result = studentMapper.insert(student);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "学生创建失败");
        }
        
        log.info("学生创建成功: {}", student.getName());
        return findById(student.getStudentId());
    }
    
    @Override
    @Transactional
    public Student updateStudent(Student student) {
        validateStudentForUpdate(student);
        
        // 检查学生是否存在
        Student existingStudent = findById(student.getStudentId());
        
        // 检查学生姓名是否在同一班级内存在（排除当前学生）
        if (existsByNameAndClassIdExcludeId(student.getName(), student.getClassId(), student.getStudentId())) {
            throw new BusinessException(ResultCode.DATA_EXISTS, "该班级内已存在同名学生");
        }
        
        int result = studentMapper.update(student);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "学生更新失败");
        }
        
        log.info("学生更新成功: ID={}", student.getStudentId());
        return findById(student.getStudentId());
    }
    
    @Override
    @Transactional
    public void deleteStudent(Long studentId) {
        if (studentId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生ID不能为空");
        }
        
        // 检查学生是否存在
        Student student = findById(studentId);
        
        int result = studentMapper.deleteById(studentId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "学生删除失败");
        }
        
        log.info("学生删除成功: ID={}", studentId);
    }
    
    @Override
    public boolean existsByNameAndClassId(String name, Long classId) {
        if (!StringUtils.hasText(name) || classId == null) {
            return false;
        }
        return studentMapper.existsByNameAndClassId(name, classId);
    }
    
    @Override
    public boolean existsByNameAndClassIdExcludeId(String name, Long classId, Long studentId) {
        if (!StringUtils.hasText(name) || classId == null || studentId == null) {
            return false;
        }
        return studentMapper.existsByNameAndClassIdExcludeId(name, classId, studentId);
    }
    
    @Override
    public List<Student> findByIds(List<Long> studentIds) {
        if (studentIds == null || studentIds.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生ID列表不能为空");
        }
        return studentMapper.findByIds(studentIds);
    }
    
    @Override
    public boolean hasPermissionToAccess(Long studentId, Long userSchoolId, String userRole) {
        if (studentId == null || userSchoolId == null || !StringUtils.hasText(userRole)) {
            return false;
        }
        
        Student student = studentMapper.findById(studentId);
        if (student == null) {
            return false;
        }
        
        // 超级管理员有所有权限
        if ("super_admin".equals(userRole)) {
            return true;
        }
        
        // 校长和老师只能访问本校的学生
        return student.getSchoolId().equals(userSchoolId);
    }
    
    private void validateStudentForCreate(Student student) {
        if (student == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生信息不能为空");
        }
        if (student.getSchoolId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (!StringUtils.hasText(student.getName())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生姓名不能为空");
        }
        if (student.getClassId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "班级ID不能为空");
        }
    }
    
    private void validateStudentForUpdate(Student student) {
        validateStudentForCreate(student);
        if (student.getStudentId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生ID不能为空");
        }
    }
}

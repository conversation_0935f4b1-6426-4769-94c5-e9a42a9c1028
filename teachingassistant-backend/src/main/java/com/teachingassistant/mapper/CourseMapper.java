package com.teachingassistant.mapper;

import com.teachingassistant.entity.Course;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 课程数据访问层
 * 
 * <AUTHOR> Assistant System
 */
@Mapper
public interface CourseMapper {
    
    /**
     * 根据课程ID查询课程
     */
    Course findById(@Param("courseId") Long courseId);
    
    /**
     * 根据学校ID查询课程列表
     */
    List<Course> findBySchoolId(@Param("schoolId") Long schoolId);
    
    /**
     * 查询指定日期范围内的课程
     */
    List<Course> findByDateRange(@Param("schoolId") Long schoolId,
                                @Param("startDate") LocalDate startDate,
                                @Param("endDate") LocalDate endDate,
                                @Param("teacherId") Long teacherId,
                                @Param("classroomId") Long classroomId);
    
    /**
     * 查询教师在指定日期的课程
     */
    List<Course> findByTeacherAndDate(@Param("teacherId") Long teacherId,
                                     @Param("courseDate") LocalDate courseDate);
    
    /**
     * 查询教室在指定日期的课程
     */
    List<Course> findByClassroomAndDate(@Param("classroomId") Long classroomId,
                                       @Param("courseDate") LocalDate courseDate);
    
    /**
     * 查询教师在指定日期范围内的课程
     */
    List<Course> findByTeacherAndDateRange(@Param("teacherId") Long teacherId,
                                          @Param("startDate") LocalDate startDate,
                                          @Param("endDate") LocalDate endDate);
    
    /**
     * 查询教室在指定日期范围内的课程
     */
    List<Course> findByClassroomAndDateRange(@Param("classroomId") Long classroomId,
                                            @Param("startDate") LocalDate startDate,
                                            @Param("endDate") LocalDate endDate);
    
    /**
     * 检查时间冲突
     */
    List<Course> findConflictCourses(@Param("teacherId") Long teacherId,
                                    @Param("classroomId") Long classroomId,
                                    @Param("courseDate") LocalDate courseDate,
                                    @Param("startTime") LocalTime startTime,
                                    @Param("endTime") LocalTime endTime,
                                    @Param("excludeCourseId") Long excludeCourseId);
    
    /**
     * 分页查询课程列表
     */
    List<Course> findWithPagination(@Param("offset") Integer offset,
                                   @Param("size") Integer size,
                                   @Param("schoolId") Long schoolId,
                                   @Param("teacherId") Long teacherId,
                                   @Param("classroomId") Long classroomId,
                                   @Param("status") String status);
    
    /**
     * 统计课程总数
     */
    Integer countCourses(@Param("schoolId") Long schoolId,
                        @Param("teacherId") Long teacherId,
                        @Param("classroomId") Long classroomId,
                        @Param("status") String status);
    
    /**
     * 插入课程
     */
    int insert(Course course);
    
    /**
     * 更新课程信息
     */
    int update(Course course);
    
    /**
     * 删除课程
     */
    int deleteById(@Param("courseId") Long courseId);
    
    /**
     * 批量删除课程
     */
    int deleteByIds(@Param("courseIds") List<Long> courseIds);
    
    /**
     * 更新课程状态
     */
    int updateStatus(@Param("courseId") Long courseId, @Param("status") String status);
}

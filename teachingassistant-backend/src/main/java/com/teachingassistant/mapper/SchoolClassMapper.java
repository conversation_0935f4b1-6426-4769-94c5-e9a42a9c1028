package com.teachingassistant.mapper;

import com.teachingassistant.entity.SchoolClass;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 班级数据访问层
 * 
 * <AUTHOR> Assistant System
 */
@Mapper
public interface SchoolClassMapper {
    
    /**
     * 根据班级ID查询班级
     */
    SchoolClass findById(@Param("classId") Long classId);
    
    /**
     * 根据学校ID查询班级列表
     */
    List<SchoolClass> findBySchoolId(@Param("schoolId") Long schoolId);
    
    /**
     * 查询所有班级列表
     */
    List<SchoolClass> findAll();
    
    /**
     * 分页查询班级列表（管理员端）
     */
    List<SchoolClass> findWithPagination(@Param("offset") Integer offset, 
                                        @Param("size") Integer size,
                                        @Param("schoolId") Long schoolId,
                                        @Param("name") String name);
    
    /**
     * 分页查询班级列表（校长端）
     */
    List<SchoolClass> findBySchoolWithPagination(@Param("offset") Integer offset, 
                                                @Param("size") Integer size,
                                                @Param("schoolId") Long schoolId,
                                                @Param("name") String name);
    
    /**
     * 统计班级总数（管理员端）
     */
    Integer countClasses(@Param("schoolId") Long schoolId,
                        @Param("name") String name);
    
    /**
     * 统计班级总数（校长端）
     */
    Integer countBySchool(@Param("schoolId") Long schoolId,
                         @Param("name") String name);
    
    /**
     * 插入班级
     */
    int insert(SchoolClass schoolClass);
    
    /**
     * 更新班级信息
     */
    int update(SchoolClass schoolClass);
    
    /**
     * 根据ID删除班级
     */
    int deleteById(@Param("classId") Long classId);
    
    /**
     * 检查班级名称是否在同一学校内存在
     */
    boolean existsByNameAndSchoolId(@Param("name") String name, @Param("schoolId") Long schoolId);
    
    /**
     * 检查班级名称是否在同一学校内存在（排除指定ID）
     */
    boolean existsByNameAndSchoolIdExcludeId(@Param("name") String name, 
                                           @Param("schoolId") Long schoolId, 
                                           @Param("classId") Long classId);
}

package com.teachingassistant.mapper;

import com.teachingassistant.entity.Student;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生数据访问层
 * 
 * <AUTHOR> Assistant System
 */
@Mapper
public interface StudentMapper {
    
    /**
     * 根据学生ID查询学生
     */
    Student findById(@Param("studentId") Long studentId);
    
    /**
     * 根据学校ID查询学生列表
     */
    List<Student> findBySchoolId(@Param("schoolId") Long schoolId);
    
    /**
     * 根据班级ID查询学生列表
     */
    List<Student> findByClassId(@Param("classId") Long classId);
    
    /**
     * 分页查询学生列表
     */
    List<Student> findWithPagination(@Param("offset") Integer offset,
                                    @Param("size") Integer size,
                                    @Param("schoolId") Long schoolId,
                                    @Param("classId") Long classId,
                                    @Param("name") String name);
    
    /**
     * 统计学生总数
     */
    Integer countStudents(@Param("schoolId") Long schoolId,
                         @Param("classId") Long classId,
                         @Param("name") String name);
    
    /**
     * 插入学生
     */
    int insert(Student student);
    
    /**
     * 更新学生信息
     */
    int update(Student student);
    
    /**
     * 删除学生
     */
    int deleteById(@Param("studentId") Long studentId);
    
    /**
     * 检查学生姓名是否在同一班级内存在
     */
    boolean existsByNameAndClassId(@Param("name") String name, @Param("classId") Long classId);
    
    /**
     * 检查学生姓名是否在同一班级内存在（排除指定ID）
     */
    boolean existsByNameAndClassIdExcludeId(@Param("name") String name, 
                                           @Param("classId") Long classId, 
                                           @Param("studentId") Long studentId);
    
    /**
     * 根据多个学生ID查询学生列表
     */
    List<Student> findByIds(@Param("studentIds") List<Long> studentIds);
}

package com.teachingassistant.controller;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.Course;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.CourseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 课程管理控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class CourseController {

    private final CourseService courseService;

    /**
     * 获取课程列表（校长端 - 分页）
     */
    @GetMapping("/principal/courses")
    public Result<PageResult<Course>> getPrincipalCourseList(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long teacherId,
            @RequestParam(required = false) Long classroomId,
            @RequestParam(required = false) String status) {
        try {
            PageResult<Course> result = courseService.findWithPagination(
                page, size, userPrincipal.getSchoolId(), teacherId, classroomId, status);
            return Result.success("获取课程列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取课程列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取指定日期范围内的课程（校长端）
     */
    @GetMapping("/principal/courses/date-range")
    public Result<List<Course>> getPrincipalCoursesByDateRange(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long teacherId,
            @RequestParam(required = false) Long classroomId) {
        try {
            List<Course> courses = courseService.findByDateRange(
                userPrincipal.getSchoolId(), startDate, endDate, teacherId, classroomId);
            return Result.success("获取课程列表成功", courses);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取课程列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取空闲时段（校长端）
     */
    @GetMapping("/principal/courses/available-slots")
    public Result<Map<LocalDate, List<Map<String, Object>>>> getAvailableTimeSlots(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam Long teacherId,
            @RequestParam Long classroomId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            Map<LocalDate, List<Map<String, Object>>> availableSlots = 
                courseService.getAvailableTimeSlots(userPrincipal.getSchoolId(), 
                                                   teacherId, classroomId, startDate, endDate);
            return Result.success("获取空闲时段成功", availableSlots);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取空闲时段失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 根据ID获取课程详情（校长端）
     */
    @GetMapping("/principal/courses/{courseId}")
    public Result<Course> getPrincipalCourseById(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long courseId) {
        try {
            // 验证权限
            if (!courseService.hasPermissionToAccess(courseId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权访问该课程");
            }
            
            Course course = courseService.findById(courseId);
            return Result.success("获取课程详情成功", course);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取课程详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 创建课程（校长端）
     */
    @PostMapping("/principal/courses")
    public Result<Course> createPrincipalCourse(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Valid @RequestBody Course course) {
        try {
            // 设置学校ID
            course.setSchoolId(userPrincipal.getSchoolId());
            
            Course createdCourse = courseService.createCourse(course);
            return Result.success("课程创建成功", createdCourse);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建课程失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 更新课程信息（校长端）
     */
    @PutMapping("/principal/courses/{courseId}")
    public Result<Course> updatePrincipalCourse(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long courseId,
            @Valid @RequestBody Course course) {
        try {
            // 验证权限
            if (!courseService.hasPermissionToAccess(courseId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权修改该课程");
            }
            
            course.setCourseId(courseId);
            course.setSchoolId(userPrincipal.getSchoolId());
            
            Course updatedCourse = courseService.updateCourse(course);
            return Result.success("课程更新成功", updatedCourse);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新课程失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 删除课程（校长端）
     */
    @DeleteMapping("/principal/courses/{courseId}")
    public Result<Void> deletePrincipalCourse(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long courseId) {
        try {
            // 验证权限
            if (!courseService.hasPermissionToAccess(courseId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权删除该课程");
            }
            
            courseService.deleteCourse(courseId);
            return Result.success("课程删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除课程失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 批量删除课程（校长端）
     */
    @DeleteMapping("/principal/courses")
    public Result<Void> deletePrincipalCourses(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody List<Long> courseIds) {
        try {
            // 验证权限（检查所有课程是否都属于当前用户的学校）
            for (Long courseId : courseIds) {
                if (!courseService.hasPermissionToAccess(courseId, 
                        userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                    throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权删除部分课程");
                }
            }
            
            courseService.deleteCourses(courseIds);
            return Result.success("批量删除课程成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("批量删除课程失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 更新课程状态（校长端）
     */
    @PatchMapping("/principal/courses/{courseId}/status")
    public Result<Void> updatePrincipalCourseStatus(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long courseId,
            @RequestParam String status) {
        try {
            // 验证权限
            if (!courseService.hasPermissionToAccess(courseId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权修改该课程状态");
            }
            
            courseService.updateCourseStatus(courseId, status);
            return Result.success("课程状态更新成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新课程状态失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取课程统计信息（校长端）
     */
    @GetMapping("/principal/courses/statistics")
    public Result<Map<String, Object>> getPrincipalCourseStatistics(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            Map<String, Object> statistics = courseService.getCourseStatistics(
                userPrincipal.getSchoolId(), startDate, endDate);
            return Result.success("获取课程统计成功", statistics);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取课程统计失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取课程列表（管理员端 - 分页）
     */
    @GetMapping("/admin/courses")
    public Result<PageResult<Course>> getAdminCourseList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long schoolId,
            @RequestParam(required = false) Long teacherId,
            @RequestParam(required = false) Long classroomId,
            @RequestParam(required = false) String status) {
        try {
            PageResult<Course> result = courseService.findWithPagination(
                page, size, schoolId, teacherId, classroomId, status);
            return Result.success("获取课程列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取课程列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 根据ID获取课程详情（管理员端）
     */
    @GetMapping("/admin/courses/{courseId}")
    public Result<Course> getAdminCourseById(@PathVariable Long courseId) {
        try {
            Course course = courseService.findById(courseId);
            return Result.success("获取课程详情成功", course);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取课程详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
}

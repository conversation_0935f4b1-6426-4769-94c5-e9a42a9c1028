package com.teachingassistant.controller;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.Student;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.StudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 学生管理控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class StudentController {

    private final StudentService studentService;

    /**
     * 获取学生列表（校长端 - 分页）
     */
    @GetMapping("/principal/students")
    public Result<PageResult<Student>> getPrincipalStudentList(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long classId,
            @RequestParam(required = false) String name) {
        try {
            PageResult<Student> result = studentService.findWithPagination(
                page, size, userPrincipal.getSchoolId(), classId, name);
            return Result.success("获取学生列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取学生列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取所有学生列表（校长端 - 不分页）
     */
    @GetMapping("/principal/students/all")
    public Result<List<Student>> getAllPrincipalStudents(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(required = false) Long classId) {
        try {
            log.info("获取学生列表请求: userPrincipal={}, classId={}", userPrincipal, classId);

            if (userPrincipal == null) {
                log.error("用户认证信息为空");
                throw new BusinessException(ResultCode.UNAUTHORIZED, "用户认证信息为空");
            }

            if (userPrincipal.getSchoolId() == null) {
                log.error("用户学校ID为空: userId={}", userPrincipal.getUserId());
                throw new BusinessException(ResultCode.PARAM_INVALID, "用户学校ID为空");
            }

            List<Student> students;
            if (classId != null) {
                log.info("根据班级ID查询学生: classId={}", classId);
                students = studentService.findByClassId(classId);
            } else {
                log.info("根据学校ID查询学生: schoolId={}", userPrincipal.getSchoolId());
                students = studentService.findBySchoolId(userPrincipal.getSchoolId());
            }

            log.info("查询到学生数量: {}", students != null ? students.size() : 0);
            return Result.success("获取学生列表成功", students);
        } catch (BusinessException e) {
            log.error("业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("获取学生列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取学生列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取学生详情（校长端）
     */
    @GetMapping("/principal/students/{studentId}")
    public Result<Student> getPrincipalStudentById(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long studentId) {
        try {
            // 验证权限
            if (!studentService.hasPermissionToAccess(studentId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权访问该学生");
            }
            
            Student student = studentService.findById(studentId);
            return Result.success("获取学生详情成功", student);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取学生详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 创建学生（校长端）
     */
    @PostMapping("/principal/students")
    public Result<Student> createPrincipalStudent(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Valid @RequestBody Student student) {
        try {
            // 设置学校ID
            student.setSchoolId(userPrincipal.getSchoolId());
            
            Student createdStudent = studentService.createStudent(student);
            return Result.success("学生创建成功", createdStudent);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建学生失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 更新学生信息（校长端）
     */
    @PutMapping("/principal/students/{studentId}")
    public Result<Student> updatePrincipalStudent(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long studentId,
            @Valid @RequestBody Student student) {
        try {
            // 验证权限
            if (!studentService.hasPermissionToAccess(studentId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权修改该学生");
            }
            
            student.setStudentId(studentId);
            student.setSchoolId(userPrincipal.getSchoolId());
            
            Student updatedStudent = studentService.updateStudent(student);
            return Result.success("学生更新成功", updatedStudent);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新学生失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 删除学生（校长端）
     */
    @DeleteMapping("/principal/students/{studentId}")
    public Result<Void> deletePrincipalStudent(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long studentId) {
        try {
            // 验证权限
            if (!studentService.hasPermissionToAccess(studentId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权删除该学生");
            }
            
            studentService.deleteStudent(studentId);
            return Result.success("学生删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除学生失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 根据多个ID获取学生列表（校长端）
     */
    @PostMapping("/principal/students/by-ids")
    public Result<List<Student>> getPrincipalStudentsByIds(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody List<Long> studentIds) {
        try {
            // 验证权限（检查所有学生是否都属于当前用户的学校）
            for (Long studentId : studentIds) {
                if (!studentService.hasPermissionToAccess(studentId, 
                        userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                    throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权访问部分学生");
                }
            }
            
            List<Student> students = studentService.findByIds(studentIds);
            return Result.success("获取学生列表成功", students);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取学生列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取学生列表（管理员端 - 分页）
     */
    @GetMapping("/admin/students")
    public Result<PageResult<Student>> getAdminStudentList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long schoolId,
            @RequestParam(required = false) Long classId,
            @RequestParam(required = false) String name) {
        try {
            PageResult<Student> result = studentService.findWithPagination(
                page, size, schoolId, classId, name);
            return Result.success("获取学生列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取学生列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 根据ID获取学生详情（管理员端）
     */
    @GetMapping("/admin/students/{studentId}")
    public Result<Student> getAdminStudentById(@PathVariable Long studentId) {
        try {
            Student student = studentService.findById(studentId);
            return Result.success("获取学生详情成功", student);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取学生详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 创建学生（管理员端）
     */
    @PostMapping("/admin/students")
    public Result<Student> createAdminStudent(@Valid @RequestBody Student student) {
        try {
            Student createdStudent = studentService.createStudent(student);
            return Result.success("学生创建成功", createdStudent);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建学生失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 更新学生信息（管理员端）
     */
    @PutMapping("/admin/students/{studentId}")
    public Result<Student> updateAdminStudent(
            @PathVariable Long studentId,
            @Valid @RequestBody Student student) {
        try {
            student.setStudentId(studentId);
            Student updatedStudent = studentService.updateStudent(student);
            return Result.success("学生更新成功", updatedStudent);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新学生失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 删除学生（管理员端）
     */
    @DeleteMapping("/admin/students/{studentId}")
    public Result<Void> deleteAdminStudent(@PathVariable Long studentId) {
        try {
            studentService.deleteStudent(studentId);
            return Result.success("学生删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除学生失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
}

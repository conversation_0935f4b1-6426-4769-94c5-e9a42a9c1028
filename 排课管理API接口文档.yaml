openapi: 3.0.0
info:
  title: 排课管理系统API
  description: 助教排课系统的课程管理、学生管理相关API接口
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 本地开发环境

paths:
  # 课程管理API
  /principal/courses:
    get:
      tags:
        - 课程管理
      summary: 获取课程列表（校长端-分页）
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            default: 10
        - name: teacherId
          in: query
          schema:
            type: integer
        - name: classroomId
          in: query
          schema:
            type: integer
        - name: status
          in: query
          schema:
            type: string
            enum: [scheduled, completed, cancelled]
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResultCourse'
    post:
      tags:
        - 课程管理
      summary: 创建课程（校长端）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCourseRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseResponse'

  /principal/courses/date-range:
    get:
      tags:
        - 课程管理
      summary: 获取指定日期范围内的课程（校长端）
      parameters:
        - name: startDate
          in: query
          required: true
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          required: true
          schema:
            type: string
            format: date
        - name: teacherId
          in: query
          schema:
            type: integer
        - name: classroomId
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseListResponse'

  /principal/courses/available-slots:
    get:
      tags:
        - 课程管理
      summary: 获取空闲时段（校长端）
      parameters:
        - name: teacherId
          in: query
          required: true
          schema:
            type: integer
        - name: classroomId
          in: query
          required: true
          schema:
            type: integer
        - name: startDate
          in: query
          required: true
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          required: true
          schema:
            type: string
            format: date
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableSlotsResponse'

  /principal/courses/{courseId}:
    get:
      tags:
        - 课程管理
      summary: 根据ID获取课程详情（校长端）
      parameters:
        - name: courseId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseResponse'
    put:
      tags:
        - 课程管理
      summary: 更新课程信息（校长端）
      parameters:
        - name: courseId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCourseRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseResponse'
    delete:
      tags:
        - 课程管理
      summary: 删除课程（校长端）
      parameters:
        - name: courseId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /principal/courses/{courseId}/status:
    patch:
      tags:
        - 课程管理
      summary: 更新课程状态（校长端）
      parameters:
        - name: courseId
          in: path
          required: true
          schema:
            type: integer
        - name: status
          in: query
          required: true
          schema:
            type: string
            enum: [scheduled, completed, cancelled]
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /principal/courses/statistics:
    get:
      tags:
        - 课程管理
      summary: 获取课程统计信息（校长端）
      parameters:
        - name: startDate
          in: query
          required: true
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          required: true
          schema:
            type: string
            format: date
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CourseStatisticsResponse'

  # 学生管理API
  /principal/students:
    get:
      tags:
        - 学生管理
      summary: 获取学生列表（校长端-分页）
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            default: 10
        - name: classId
          in: query
          schema:
            type: integer
        - name: name
          in: query
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResultStudent'
    post:
      tags:
        - 学生管理
      summary: 创建学生（校长端）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStudentRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentResponse'

  /principal/students/all:
    get:
      tags:
        - 学生管理
      summary: 获取所有学生列表（校长端-不分页）
      parameters:
        - name: classId
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentListResponse'

  /principal/students/{studentId}:
    get:
      tags:
        - 学生管理
      summary: 根据ID获取学生详情（校长端）
      parameters:
        - name: studentId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentResponse'
    put:
      tags:
        - 学生管理
      summary: 更新学生信息（校长端）
      parameters:
        - name: studentId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStudentRequest'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentResponse'
    delete:
      tags:
        - 学生管理
      summary: 删除学生（校长端）
      parameters:
        - name: studentId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /principal/students/by-ids:
    post:
      tags:
        - 学生管理
      summary: 根据多个ID获取学生列表（校长端）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentListResponse'

  # 教师管理API
  /principal/teachers/all:
    get:
      tags:
        - 教师管理
      summary: 获取所有教师列表（校长端-不分页）
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeacherListResponse'

  # 教室管理API
  /principal/classrooms/all:
    get:
      tags:
        - 教室管理
      summary: 获取所有教室列表（校长端-不分页）
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomListResponse'

components:
  schemas:
    BaseResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "操作成功"
        data:
          type: object

    Course:
      type: object
      properties:
        courseId:
          type: integer
        schoolId:
          type: integer
        teacherId:
          type: integer
        studentId:
          type: integer
        classroomId:
          type: integer
        courseDate:
          type: string
          format: date
        startTime:
          type: string
          format: time
        endTime:
          type: string
          format: time
        price:
          type: number
          format: decimal
        gradeLevel:
          type: string
        status:
          type: string
          enum: [scheduled, completed, cancelled]
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        teacher:
          $ref: '#/components/schemas/Teacher'
        student:
          $ref: '#/components/schemas/Student'
        classroom:
          $ref: '#/components/schemas/Classroom'

    CreateCourseRequest:
      type: object
      required:
        - teacherId
        - studentId
        - classroomId
        - courseDate
        - startTime
        - endTime
        - price
      properties:
        teacherId:
          type: integer
        studentId:
          type: integer
        classroomId:
          type: integer
        courseDate:
          type: string
          format: date
        startTime:
          type: string
          format: time
        endTime:
          type: string
          format: time
        price:
          type: number
          format: decimal
        gradeLevel:
          type: string

    UpdateCourseRequest:
      type: object
      properties:
        teacherId:
          type: integer
        studentId:
          type: integer
        classroomId:
          type: integer
        courseDate:
          type: string
          format: date
        startTime:
          type: string
          format: time
        endTime:
          type: string
          format: time
        price:
          type: number
          format: decimal
        gradeLevel:
          type: string
        status:
          type: string
          enum: [scheduled, completed, cancelled]

    Student:
      type: object
      properties:
        studentId:
          type: integer
        schoolId:
          type: integer
        name:
          type: string
        classId:
          type: integer
        contactPhone:
          type: string
        unpaidAmount:
          type: number
          format: decimal
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateStudentRequest:
      type: object
      required:
        - name
        - classId
      properties:
        name:
          type: string
        classId:
          type: integer
        contactPhone:
          type: string
        unpaidAmount:
          type: number
          format: decimal

    UpdateStudentRequest:
      type: object
      properties:
        name:
          type: string
        classId:
          type: integer
        contactPhone:
          type: string
        unpaidAmount:
          type: number
          format: decimal

    Teacher:
      type: object
      properties:
        userId:
          type: integer
        realName:
          type: string
        phone:
          type: string
        email:
          type: string
        subject:
          type: string
        experience:
          type: integer

    Classroom:
      type: object
      properties:
        classroomId:
          type: integer
        name:
          type: string
        floor:
          type: integer
        type:
          type: string
          enum: [normal, multimedia, lab]
        capacity:
          type: integer
        status:
          type: string
          enum: [available, in_use, maintenance]

    TimeSlot:
      type: object
      properties:
        startTime:
          type: string
          format: time
        endTime:
          type: string
          format: time
        available:
          type: boolean

    CourseStatistics:
      type: object
      properties:
        totalCourses:
          type: integer
        scheduledCourses:
          type: integer
        completedCourses:
          type: integer
        cancelledCourses:
          type: integer

    PageResult:
      type: object
      properties:
        page:
          type: integer
        size:
          type: integer
        total:
          type: integer
        records:
          type: array
          items:
            type: object

    CourseResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Course'

    CourseListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Course'

    PageResultCourse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              allOf:
                - $ref: '#/components/schemas/PageResult'
                - type: object
                  properties:
                    records:
                      type: array
                      items:
                        $ref: '#/components/schemas/Course'

    AvailableSlotsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              additionalProperties:
                type: array
                items:
                  $ref: '#/components/schemas/TimeSlot'

    CourseStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/CourseStatistics'

    StudentResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Student'

    StudentListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Student'

    PageResultStudent:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              allOf:
                - $ref: '#/components/schemas/PageResult'
                - type: object
                  properties:
                    records:
                      type: array
                      items:
                        $ref: '#/components/schemas/Student'

    TeacherListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Teacher'

    ClassroomListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Classroom'

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []
